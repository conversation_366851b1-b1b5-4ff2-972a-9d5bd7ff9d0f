import { Component, inject, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SelectModule } from 'primeng/select';
import { Countries } from '../../shared/constant/countries';
import { InputTextModule } from 'primeng/inputtext';
import { InputOtpModule } from 'primeng/inputotp';
import { FloatLabelModule } from 'primeng/floatlabel';
import { CommonModule } from '@angular/common';
import { PasswordModule } from 'primeng/password';
import { AuthService } from '../../services/auth.service';
import { uppercaseValidator, lowercaseValidator, numberValidator } from '../../shared/validators/validators';
import { IftaLabelModule } from 'primeng/iftalabel';
import { Router } from '@angular/router';

@Component({
  selector: 'app-signup',
  imports: [TranslateModule,FormsModule,
    ReactiveFormsModule,SelectModule,
    InputTextModule,InputOtpModule,
    FloatLabelModule,CommonModule,
    PasswordModule, IftaLabelModule],
  templateUrl: './signup.component.html',
  styleUrl: './signup.component.scss'
})
export class SignupComponent implements OnInit {
    translateService = inject(TranslateService)
    fb = inject(FormBuilder)
    authService = inject(AuthService)
    router = inject(Router)
    countries: Countries[] | undefined;
    signUpForm !: FormGroup;
    OTPControl !: FormControl
    step = 1 ;
    ngOnInit(): void {
    this.countries = Countries
    this.signUpForm = this.fb.group({
        phoneNumber: [null,[Validators.required,Validators.minLength(8)]],
        firstName: [null,[Validators.required]],
        lastName: [null,[Validators.required]],
        password: [null,[Validators.required,Validators.minLength(8),
                        Validators.maxLength(255),
                        uppercaseValidator,
                        lowercaseValidator,
                        numberValidator]],

    })
    this.OTPControl = new FormControl(null,[Validators.required,Validators.minLength(6)])
    }

    resendOTP(){
     this.authService.sendOTP(this.signUpForm.get('phoneNumber')?.value.toString())
    }

    signUp(){
      const credentials = {first_name:this.signUpForm.get('firstName')?.value,
        last_name:this.signUpForm.get('lastName')?.value,
        phone_number:this.signUpForm.get('phoneNumber')?.value.toString(),
        password:this.signUpForm.get('password')?.value,
      }
      console.log(credentials)
      this.authService.signUp(credentials).subscribe((value) => {
      if(value.data) this.step = 3
    });
    }

    verifyAccount(){
     this.authService.verifyAccount(this.signUpForm.get('phoneNumber')?.value.toString()
        ,this.OTPControl?.value.toString())
    }

    back(){
    if(this.step === 1){
        this.router.navigate(['/welcome'])
        return
    }
     this.step --
    }
}
