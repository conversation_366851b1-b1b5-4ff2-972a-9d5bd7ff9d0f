<div class="grid">
    <div class="col-12">
        <h2 class="mb-4">Drivers Evaluation</h2>
        <div class="grid">
            <!-- Empty state -->
            @if (drivers().length === 0) {
                <div
                    class="col-12 justify-content-center align-items-center flex"
                    style="min-height: 200px"
                >
                    <p>No pending driver applications found.</p>
                </div>
            }

            <!-- Driver cards -->
            @for (driver of drivers(); track driver.id) {
                <div class="col-12 md:col-6 lg:col-4 mb-3">
                    <p-card>
                        <ng-template pTemplate="header">
                            <div class="bg-primary p-3 text-white">
                                <h3>
                                    {{ driver.firstName }} {{ driver.lastName }}
                                </h3>
                                <span class="text-sm"
                                    >Status: {{ driver.driverStatus }}</span
                                >
                            </div>
                        </ng-template>

                        <div class="mb-3 flex">
                            <div class="flex-grow">
                                <!-- Driver info -->
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-phone mr-2 text-primary"
                                    ></i>
                                    <span class="font-semibold">Phone:</span>
                                    <span class="ml-2">{{
                                        driver.phoneNumber
                                    }}</span>
                                </div>
                                <!-- Car info if available -->
                                @if (driver.car) {
                                    <div class="align-items-center mb-2 flex">
                                        <i
                                            class="pi pi-car mr-2 text-primary"
                                        ></i>
                                        <span class="font-semibold">Car:</span>
                                        <span class="ml-2"
                                            >{{ driver.car.make }}
                                            {{ driver.car.model }} ({{
                                                driver.car.year
                                            }})</span
                                        >
                                    </div>
                                    <div class="align-items-center mb-2 flex">
                                        <i
                                            class="pi pi-id-card mr-2 text-primary"
                                        ></i>
                                        <span class="font-semibold"
                                            >License Plate:</span
                                        >
                                        <span class="ml-2">{{
                                            driver.car.licensePlate
                                        }}</span>
                                    </div>
                                }
                                <!-- ID verification status -->
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-check-circle mr-2"
                                        [ngClass]="
                                            driver.isPhoneVerified
                                                ? 'text-green-500'
                                                : 'text-red-500'
                                        "
                                    ></i>
                                    <span class="font-semibold"
                                        >Phone Verified:</span
                                    >
                                    <span class="ml-2">{{
                                        driver.isPhoneVerified ? "Yes" : "No"
                                    }}</span>
                                </div>
                                <!-- Document status -->
                                <div class="align-items-center mb-2 flex">
                                    <i
                                        class="pi pi-file mr-2"
                                        [ngClass]="
                                            driver.IdCardFrontUrl
                                                ? 'text-green-500'
                                                : 'text-red-500'
                                        "
                                    ></i>
                                    <span class="font-semibold"
                                        >ID Documents:</span
                                    >
                                    <div class="ml-2 flex gap-2">
                                        <button
                                            pButton
                                            type="button"
                                            label="Front"
                                            class="p-button-sm p-button-outlined"
                                            [disabled]="!driver.IdCardFrontUrl"
                                            (click)="
                                                viewDocument(
                                                    imagesLink +
                                                        driver.IdCardFrontUrl
                                                )
                                            "
                                        ></button>
                                        <button
                                            pButton
                                            type="button"
                                            label="Back"
                                            class="p-button-sm p-button-outlined"
                                            [disabled]="!driver.IdCardBackUrl"
                                            (click)="
                                                viewDocument(
                                                    imagesLink +
                                                        driver.IdCardBackUrl
                                                )
                                            "
                                        ></button>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p-image
                                    [src]="
                                        driver.PersonalPhotoUrl
                                            ? imagesLink +
                                              driver.PersonalPhotoUrl
                                            : 'assets/images/profile-placeholder.png'
                                    "
                                    [preview]="true"
                                    alt="Profile Photo"
                                    width="250"
                                    (error)="onImageError($event)"
                                >
                                    <ng-template #indicator>
                                        <i class="pi pi-search"></i>
                                    </ng-template>
                                    <ng-template #image>
                                        <img
                                            [src]="
                                                driver.PersonalPhotoUrl
                                                    ? imagesLink +
                                                      driver.PersonalPhotoUrl
                                                    : 'assets/images/profile-placeholder.png'
                                            "
                                            alt="Profile Photo"
                                            width="250"
                                            (error)="onImageError($event)"
                                        />
                                    </ng-template>
                                    <ng-template
                                        #preview
                                        let-style="style"
                                        let-previewCallback="previewCallback"
                                    >
                                        <img
                                            [src]="
                                                driver.PersonalPhotoUrl
                                                    ? imagesLink +
                                                      driver.PersonalPhotoUrl
                                                    : 'assets/images/profile-placeholder.png'
                                            "
                                            alt="Profile Photo"
                                            [style]="style"
                                            (click)="previewCallback()"
                                            (error)="onImageError($event)"
                                        />
                                    </ng-template>
                                </p-image>
                            </div>
                        </div>

                        <!-- Action buttons -->
                        <div class="justify-content-between flex">
                            <button
                                pButton
                                icon="pi pi-times"
                                label="Reject"
                                class="p-button-danger"
                                (click)="rejectDriver(driver)"
                            ></button>
                            <button
                                pButton
                                icon="pi pi-check"
                                label="Accept"
                                class="p-button-success"
                                (click)="acceptDriver(driver)"
                            ></button>
                        </div>
                    </p-card>
                </div>
            }
        </div>
    </div>
</div>
