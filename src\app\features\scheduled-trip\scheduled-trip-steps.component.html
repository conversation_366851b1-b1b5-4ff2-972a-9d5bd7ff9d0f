<div
    class="pointer-events-auto m-0 flex h-full max-h-[95dvh] flex-col overflow-auto bg-background-color-100 p-0"
>
    <!-- Header with back button and title -->
    <div
        class="flex items-center justify-between border-b border-background-color-300 bg-background-color-100 p-4"
    >
        <div class="flex items-center gap-3">
            @if (currentStep() !== ScheduledTripStep.ORDER_LOCATION) {
                <button
                    class="flex h-10 w-10 items-center justify-center rounded-full bg-background-color-200 text-text-color-100 transition-all duration-200 hover:bg-background-color-300"
                    (click)="goBack()"
                >
                    <i class="pi pi-arrow-left text-sm"></i>
                </button>
            }
            <h1 class="ms-8 text-xl font-semibold text-text-color-100">
                Schedule Trip
            </h1>
        </div>
    </div>

    <!-- Progress indicator -->
    <div
        class="border-b border-background-color-300 bg-background-color-100 p-4"
    >
        <div class="flex items-center justify-between">
            <!-- Step 1: Order Location -->
            <div class="flex flex-col items-center">
                <div
                    class="flex h-8 w-8 items-center justify-center rounded-full text-xs font-semibold"
                    [class]="
                        currentStep() >= ScheduledTripStep.ORDER_LOCATION
                            ? 'bg-main-color-600 text-white'
                            : 'bg-background-color-300 text-text-color-300'
                    "
                >
                    @if (currentStep() > ScheduledTripStep.ORDER_LOCATION) {
                        <i class="pi pi-check text-xs"></i>
                    } @else {
                        1
                    }
                </div>
                <span class="mt-1 text-xs text-text-color-300">Location</span>
            </div>

            <!-- Connector line -->
            <div
                class="mx-2 h-0.5 flex-1"
                [class]="
                    currentStep() > ScheduledTripStep.ORDER_LOCATION
                        ? 'bg-main-color-600'
                        : 'bg-background-color-300'
                "
            ></div>

            <!-- Step 2: Time -->
            <div class="flex flex-col items-center">
                <div
                    class="flex h-8 w-8 items-center justify-center rounded-full text-xs font-semibold"
                    [class]="
                        currentStep() >= ScheduledTripStep.TIME
                            ? 'bg-main-color-600 text-white'
                            : 'bg-background-color-300 text-text-color-300'
                    "
                >
                    @if (currentStep() > ScheduledTripStep.TIME) {
                        <i class="pi pi-check text-xs"></i>
                    } @else {
                        2
                    }
                </div>
                <span class="mt-1 text-xs text-text-color-300">Time</span>
            </div>

            <!-- Connector line -->
            <div
                class="mx-2 h-0.5 flex-1"
                [class]="
                    currentStep() > ScheduledTripStep.TIME
                        ? 'bg-main-color-600'
                        : 'bg-background-color-300'
                "
            ></div>

            <!-- Step 3: Near Pickup -->
            <div class="flex flex-col items-center">
                <div
                    class="flex h-8 w-8 items-center justify-center rounded-full text-xs font-semibold"
                    [class]="
                        currentStep() >= ScheduledTripStep.NEAR_PICKUP_LOCATION
                            ? 'bg-main-color-600 text-white'
                            : 'bg-background-color-300 text-text-color-300'
                    "
                >
                    @if (
                        currentStep() > ScheduledTripStep.NEAR_PICKUP_LOCATION
                    ) {
                        <i class="pi pi-check text-xs"></i>
                    } @else {
                        3
                    }
                </div>
                <span class="mt-1 text-xs text-text-color-300"
                    >Near Pickup</span
                >
            </div>
        </div>
    </div>

    <!-- Step Content -->
    <div
        class="flex h-full min-h-0 flex-1 flex-col overflow-auto bg-background-color-100"
    >
        <!-- Step 1: Order Location -->
        @if (currentStep() === ScheduledTripStep.ORDER_LOCATION) {
            <app-order-location-step
                [tripData]="tripData()"
                (completed)="onOrderLocationCompleted($event)"
            ></app-order-location-step>
        }

        <!-- Step 2: Time -->
        @if (currentStep() === ScheduledTripStep.TIME) {
            <app-time-step
                [tripData]="tripData()"
                (completed)="onTimeCompleted($event)"
            ></app-time-step>
        }

        <!-- Step 3: Near Pickup Location -->
        @if (currentStep() === ScheduledTripStep.NEAR_PICKUP_LOCATION) {
            <app-near-pickup-location-step
                [tripData]="tripData()"
                (completed)="onNearPickupLocationCompleted($event)"
            ></app-near-pickup-location-step>
        }

        <!-- Step 4: Confirm Scheduled Trip -->
        @if (currentStep() === ScheduledTripStep.CONFIRM_SCHEDULED_TRIP) {
            <div class="flex flex-1 flex-col bg-background-color-100 p-6">
                <div class="flex flex-1 flex-col justify-center">
                    <div class="mb-8 text-center">
                        <i
                            class="pi pi-calendar-plus mb-4 text-6xl text-main-color-600"
                        ></i>
                        <h2
                            class="mb-2 text-2xl font-semibold text-text-color-100"
                        >
                            Confirm Your Scheduled Trip
                        </h2>
                        <p class="text-text-color-300">
                            Review your trip details before scheduling
                        </p>
                    </div>

                    <!-- Trip Summary -->
                    <div class="mb-8 space-y-4">
                        <!-- Route -->
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <h3
                                class="mb-3 text-sm font-semibold text-text-color-100"
                            >
                                Route
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-start gap-3">
                                    <div
                                        class="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-white"
                                    >
                                        <i class="pi pi-circle text-xs"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p
                                            class="text-sm font-medium text-text-color-100"
                                        >
                                            From
                                        </p>
                                        <p
                                            class="truncate text-sm text-text-color-300"
                                        >
                                            {{
                                                tripData().pickupAddress ||
                                                    "Selected location"
                                            }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-3">
                                    <div
                                        class="flex h-6 w-6 items-center justify-center rounded-full bg-red-600 text-white"
                                    >
                                        <i class="pi pi-circle text-xs"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p
                                            class="text-sm font-medium text-text-color-100"
                                        >
                                            To
                                        </p>
                                        <p
                                            class="truncate text-sm text-text-color-300"
                                        >
                                            {{
                                                tripData().dropoffAddress ||
                                                    "Selected location"
                                            }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Time -->
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <h3
                                class="mb-2 text-sm font-semibold text-text-color-100"
                            >
                                Scheduled Time
                            </h3>
                            <p class="text-sm text-text-color-300">
                                {{
                                    tripData().scheduledDateTime
                                        | date: "medium"
                                }}
                            </p>
                        </div>

                        <!-- Near Pickup -->
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <h3
                                class="mb-2 text-sm font-semibold text-text-color-100"
                            >
                                Near Pickup Location
                            </h3>
                            <p class="text-sm text-text-color-300">
                                {{
                                    tripData().nearPickupAddress ||
                                        "Selected location"
                                }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Confirm Button -->
                <div class="flex-shrink-0">
                    <button
                        class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg bg-background-color-200 text-base font-semibold text-white shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400"
                        (click)="confirmScheduledTrip()"
                        [disabled]="!canConfirmTrip()"
                    >
                        <i class="pi pi-check mr-2 text-sm"></i>
                        Schedule Trip
                    </button>
                </div>
            </div>
        }
    </div>
</div>
