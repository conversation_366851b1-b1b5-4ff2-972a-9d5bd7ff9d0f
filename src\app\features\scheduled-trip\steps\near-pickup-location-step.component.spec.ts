import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { of } from 'rxjs';

import { NearPickupLocationStepComponent } from './near-pickup-location-step.component';
import { NominatimService } from '../../../services/nominatim.service';
import { StopPointService } from '../../../services/stop-point.service';
import { MapComponent } from '../../../shared/components/map.component';
import { ScheduledTripData } from '../scheduled-trip-steps.component';
import { latLng } from 'leaflet';

describe('NearPickupLocationStepComponent', () => {
    let component: NearPickupLocationStepComponent;
    let fixture: ComponentFixture<NearPickupLocationStepComponent>;
    let mockNominatimService: jasmine.SpyObj<NominatimService>;
    let mockStopPointService: jasmine.SpyObj<StopPointService>;

    const mockStopPoints = [
        { id: '1', name: 'Stop Point 1', latitude: 40.7128, longitude: -74.0060 },
        { id: '2', name: 'Stop Point 2', latitude: 40.7589, longitude: -73.9851 },
        { id: '3', name: 'Stop Point 3', latitude: 40.6892, longitude: -74.0445 },
    ];

    beforeEach(async () => {
        const nominatimSpy = jasmine.createSpyObj('NominatimService', ['reverseGeocode']);
        const stopPointSpy = jasmine.createSpyObj('StopPointService', ['getAllStopPoints']);

        await TestBed.configureTestingModule({
            imports: [
                CommonModule,
                ButtonModule,
                CardModule,
                MapComponent,
                NearPickupLocationStepComponent
            ],
            providers: [
                { provide: NominatimService, useValue: nominatimSpy },
                { provide: StopPointService, useValue: stopPointSpy }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(NearPickupLocationStepComponent);
        component = fixture.componentInstance;
        mockNominatimService = TestBed.inject(NominatimService) as jasmine.SpyObj<NominatimService>;
        mockStopPointService = TestBed.inject(StopPointService) as jasmine.SpyObj<StopPointService>;

        // Setup default mocks
        mockStopPointService.getAllStopPoints.and.returnValue(of({ data: mockStopPoints, error: null }));
        mockNominatimService.reverseGeocode.and.returnValue(of({ display_name: 'Test Address' }));
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should load stop points on init', () => {
        component.ngOnInit();
        expect(mockStopPointService.getAllStopPoints).toHaveBeenCalled();
        expect(component.allStopPoints()).toEqual(mockStopPoints);
    });

    it('should filter nearby stop points within 1000m', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060), // NYC coordinates
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        component.allStopPoints.set(mockStopPoints);
        component.filterNearbyStopPoints();

        const nearbyPoints = component.nearbyStopPoints();
        expect(nearbyPoints.length).toBeGreaterThan(0);
        
        // Check that all nearby points are within 1000m
        nearbyPoints.forEach(point => {
            const distance = component.calculateDistance(
                tripData.pickupLocation!.lat,
                tripData.pickupLocation!.lng,
                point.latitude,
                point.longitude
            );
            expect(distance).toBeLessThanOrEqual(1000);
        });
    });

    it('should calculate distance correctly', () => {
        // Test distance between two known points in NYC
        const distance = component.calculateDistance(
            40.7128, -74.0060, // NYC
            40.7589, -73.9851  // Times Square
        );
        
        // Distance should be approximately 5.7km
        expect(distance).toBeGreaterThan(5000);
        expect(distance).toBeLessThan(7000);
    });

    it('should select stop point correctly', () => {
        const stopPoint = mockStopPoints[0];
        
        component.selectStopPoint(stopPoint);
        
        expect(component.nearPickupLocation()).toBeTruthy();
        expect(component.nearPickupLocation()!.lat).toBe(stopPoint.latitude);
        expect(component.nearPickupLocation()!.lng).toBe(stopPoint.longitude);
        expect(component.nearPickupAddress()).toBe(stopPoint.name);
        expect(component.nearPickupSelected()).toBe(true);
        expect(component.showStopPointsMap()).toBe(false);
    });

    it('should generate map nodes correctly', () => {
        const tripData: ScheduledTripData = {
            pickupLocation: latLng(40.7128, -74.0060),
            dropoffLocation: latLng(40.7589, -73.9851)
        };

        fixture.componentRef.setInput('tripData', tripData);
        component.nearbyStopPoints.set([mockStopPoints[0]]);
        
        const nodes = component.mapNodes();
        
        // Should have pickup, dropoff, stop point, and route line
        expect(nodes.length).toBeGreaterThan(3);
    });

    it('should emit completed event with correct data', () => {
        const location = latLng(40.7128, -74.0060);
        const address = 'Test Address';
        
        spyOn(component.completed, 'emit');
        
        component.nearPickupLocation.set(location);
        component.nearPickupAddress.set(address);
        
        component.confirmNearPickup();
        
        expect(component.completed.emit).toHaveBeenCalledWith({
            location,
            address
        });
    });

    it('should show and hide stop points map', () => {
        expect(component.showStopPointsMap()).toBe(false);
        
        component.showStopPointsSelection();
        expect(component.showStopPointsMap()).toBe(true);
        
        component.hideStopPointsSelection();
        expect(component.showStopPointsMap()).toBe(false);
    });

    it('should reset state when changing near pickup', () => {
        // Set some state
        component.nearPickupSelected.set(true);
        component.nearPickupLocation.set(latLng(40.7128, -74.0060));
        component.nearPickupAddress.set('Test Address');
        component.showStopPointsMap.set(true);
        
        component.changeNearPickup();
        
        expect(component.nearPickupSelected()).toBe(false);
        expect(component.nearPickupLocation()).toBeNull();
        expect(component.nearPickupAddress()).toBe('');
        expect(component.showStopPointsMap()).toBe(false);
    });
});
