import { CommonModule } from '@angular/common';
import { Component, input, output, signal, computed } from '@angular/core';
import { LatLng } from 'leaflet';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { LocationPickerComponent } from '../../order-flow/location-picker.component';
import { ScheduledTripData } from '../scheduled-trip-steps.component';
import { NominatimService } from '../../../services/nominatim.service';
import { injectMany } from '../../../shared/helpers/injectMany';

@Component({
    selector: 'app-near-pickup-location-step',
    standalone: true,
    imports: [CommonModule, ButtonModule, CardModule, LocationPickerComponent],
    template: `
        <div class="flex flex-1 flex-col bg-background-color-100">
            <!-- Step Header -->
            <div
                class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
            >
                <div
                    class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-white"
                >
                    <i class="pi pi-map-marker text-lg"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-text-color-100">
                        Where should we pick you up nearby?
                    </h3>
                    <p class="text-sm text-text-color-300">
                        Choose a convenient location near your pickup point
                    </p>
                </div>
            </div>

            <!-- Trip Summary -->
            <div
                class="border-b border-background-color-300 bg-background-color-100 p-4"
            >
                <div class="space-y-3">
                    <!-- Original Pickup -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-white"
                        >
                            <i class="pi pi-circle text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Original Pickup
                            </h4>
                            <p class="truncate text-sm text-text-color-300">
                                {{
                                    tripData().pickupAddress ||
                                        'Selected location'
                                }}
                            </p>
                        </div>
                    </div>

                    <!-- Destination -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-red-600 text-white"
                        >
                            <i class="pi pi-circle text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Destination
                            </h4>
                            <p class="truncate text-sm text-text-color-300">
                                {{
                                    tripData().dropoffAddress ||
                                        'Selected location'
                                }}
                            </p>
                        </div>
                    </div>

                    <!-- Scheduled Time -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white"
                        >
                            <i class="pi pi-clock text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Scheduled Time
                            </h4>
                            <p class="text-sm text-text-color-300">
                                {{
                                    tripData().scheduledDateTime
                                        | date: 'medium'
                                }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="border-b border-background-color-300 bg-blue-50 p-4">
                <div class="flex items-start gap-3">
                    <i class="pi pi-info-circle mt-0.5 text-blue-600"></i>
                    <div>
                        <h4 class="mb-1 text-sm font-semibold text-blue-800">
                            Why choose a nearby pickup location?
                        </h4>
                        <p class="text-sm text-blue-700">
                            Selecting a nearby location helps ensure your driver
                            can easily find you and provides flexibility for
                            your scheduled pickup time.
                        </p>
                    </div>
                </div>
            </div>

            @if (!nearPickupSelected()) {
                <!-- Near Pickup Location Selection -->
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-600 text-white"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Select nearby pickup location
                        </h3>
                    </div>
                </div>

                <div class="flex h-auto min-h-0 flex-1 flex-col">
                    <app-location-picker
                        title="Near Pickup"
                        placeholder="Choose a location near your pickup point"
                        [initialLocation]="tripData().pickupLocation ?? null"
                        (locationSelected)="onNearPickupSelected($event)"
                    ></app-location-picker>
                </div>
            } @else {
                <!-- Confirmation View -->
                <div class="flex flex-1 flex-col justify-center p-6">
                    <div class="mb-8 text-center">
                        <i
                            class="pi pi-check-circle mb-4 text-4xl text-green-600"
                        ></i>
                        <h2
                            class="mb-2 text-xl font-semibold text-text-color-100"
                        >
                            Perfect! We've got your pickup location
                        </h2>
                        <p class="text-text-color-300">
                            Your driver will meet you at this nearby location
                        </p>
                    </div>

                    <!-- Near Pickup Summary -->
                    <div class="mb-8">
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <div class="flex items-start gap-4">
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-orange-600 text-white"
                                >
                                    <i class="pi pi-map-marker text-sm"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <h4
                                        class="mb-1 text-sm font-semibold text-text-color-100"
                                    >
                                        Near Pickup Location
                                    </h4>
                                    <p class="text-sm text-text-color-300">
                                        {{
                                            nearPickupAddress() ||
                                                'Selected location'
                                        }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button
                            class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg bg-background-color-200 text-base font-semibold text-white shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400"
                            (click)="confirmNearPickup()"
                        >
                            <i class="pi pi-arrow-right mr-2 text-sm"></i>
                            Continue to Confirmation
                        </button>

                        <button
                            class="flex min-h-[48px] w-full cursor-pointer items-center justify-center rounded-lg border border-background-color-300 bg-background-color-100 text-base font-medium text-text-color-100 transition-all duration-200 hover:bg-background-color-200"
                            (click)="changeNearPickup()"
                        >
                            <i class="pi pi-pencil mr-2 text-sm"></i>
                            Change Location
                        </button>
                    </div>
                </div>
            }
        </div>
    `,
})
export class NearPickupLocationStepComponent {
    services = injectMany({
        NominatimService,
    });

    // Inputs
    tripData = input<ScheduledTripData>({});

    // Outputs
    completed = output<{
        location: LatLng;
        address?: string;
    }>();

    // State
    nearPickupSelected = signal<boolean>(false);
    nearPickupLocation = signal<LatLng | null>(null);
    nearPickupAddress = signal<string>('');

    onNearPickupSelected(location: LatLng): void {
        this.nearPickupLocation.set(location);

        // Get address for the location
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.nearPickupAddress.set(result.display_name);
                }
                this.nearPickupSelected.set(true);
            },
            error: () => {
                this.nearPickupAddress.set('Selected location');
                this.nearPickupSelected.set(true);
            },
        });
    }

    confirmNearPickup(): void {
        const location = this.nearPickupLocation();
        if (location) {
            this.completed.emit({
                location,
                address: this.nearPickupAddress(),
            });
        }
    }

    changeNearPickup(): void {
        this.nearPickupSelected.set(false);
        this.nearPickupLocation.set(null);
        this.nearPickupAddress.set('');
    }
}
