import { CommonModule } from '@angular/common';
import {
    Component,
    input,
    output,
    signal,
    computed,
    OnInit,
    effect,
} from '@angular/core';
import { LatLng, latLng, marker, divIcon, polyline } from 'leaflet';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';

import { ScheduledTripData } from '../scheduled-trip-steps.component';
import { NominatimService } from '../../../services/nominatim.service';
import {
    StopPointService,
    StopPoint,
} from '../../../services/stop-point.service';
import { injectMany } from '../../../shared/helpers/injectMany';
import { MapComponent } from '../../../shared/components/map.component';
import { getRandomColor } from '../../../shared/helpers/randomColor';

@Component({
    selector: 'app-near-pickup-location-step',
    standalone: true,
    imports: [CommonModule, ButtonModule, CardModule, MapComponent],
    template: `
        <div class="flex flex-1 flex-col bg-background-color-100">
            <!-- Step Header -->
            <div
                class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
            >
                <div
                    class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-white"
                >
                    <i class="pi pi-map-marker text-lg"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-text-color-100">
                        Where should we pick you up nearby?
                    </h3>
                    <p class="text-sm text-text-color-300">
                        Choose a convenient location near your pickup point
                    </p>
                </div>
            </div>

            <!-- Trip Summary -->
            <div
                class="border-b border-background-color-300 bg-background-color-100 p-4"
            >
                <div class="space-y-3">
                    <!-- Original Pickup -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-white"
                        >
                            <i class="pi pi-circle text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Original Pickup
                            </h4>
                            <p class="truncate text-sm text-text-color-300">
                                {{
                                    tripData().pickupAddress ||
                                        'Selected location'
                                }}
                            </p>
                        </div>
                    </div>

                    <!-- Destination -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-red-600 text-white"
                        >
                            <i class="pi pi-circle text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Destination
                            </h4>
                            <p class="truncate text-sm text-text-color-300">
                                {{
                                    tripData().dropoffAddress ||
                                        'Selected location'
                                }}
                            </p>
                        </div>
                    </div>

                    <!-- Scheduled Time -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white"
                        >
                            <i class="pi pi-clock text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Scheduled Time
                            </h4>
                            <p class="text-sm text-text-color-300">
                                {{
                                    tripData().scheduledDateTime
                                        | date: 'medium'
                                }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="border-b border-background-color-300 bg-blue-50 p-4">
                <div class="flex items-start gap-3">
                    <i class="pi pi-info-circle mt-0.5 text-blue-600"></i>
                    <div>
                        <h4 class="mb-1 text-sm font-semibold text-blue-800">
                            Why choose a nearby pickup location?
                        </h4>
                        <p class="text-sm text-blue-700">
                            Selecting a nearby location helps ensure your driver
                            can easily find you and provides flexibility for
                            your scheduled pickup time.
                        </p>
                    </div>
                </div>
            </div>

            @if (!nearPickupSelected()) {
                <!-- Near Pickup Location Selection -->
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-600 text-white"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Select nearby pickup location
                        </h3>
                        <p class="text-sm text-text-color-300">
                            Choose from nearby stop points or select any
                            location
                        </p>
                    </div>
                </div>

                @if (!showStopPointsMap()) {
                    <!-- Selection Options -->
                    <div class="p-6 space-y-4">
                        @if (nearbyStopPoints().length > 0) {
                            <div class="space-y-3">
                                <h4
                                    class="text-lg font-semibold text-text-color-100"
                                >
                                    Nearby Stop Points (within 1km)
                                </h4>
                                <div class="grid gap-3">
                                    @for (
                                        stopPoint of nearbyStopPoints();
                                        track stopPoint.id
                                    ) {
                                        <button
                                            class="flex items-center gap-3 p-4 rounded-lg border border-background-color-300 bg-background-color-100 text-left transition-all duration-200 hover:border-orange-600 hover:bg-orange-50"
                                            (click)="selectStopPoint(stopPoint)"
                                        >
                                            <div
                                                class="flex h-10 w-10 items-center justify-center rounded-full bg-orange-600 text-white"
                                            >
                                                <i
                                                    class="pi pi-map-marker text-sm"
                                                ></i>
                                            </div>
                                            <div class="flex-1">
                                                <h5
                                                    class="font-medium text-text-color-100"
                                                >
                                                    {{ stopPoint.name }}
                                                </h5>
                                                <p
                                                    class="text-sm text-text-color-300"
                                                >
                                                    {{
                                                        calculateDistance(
                                                            tripData()
                                                                .pickupLocation!
                                                                .lat,
                                                            tripData()
                                                                .pickupLocation!
                                                                .lng,
                                                            stopPoint.latitude,
                                                            stopPoint.longitude
                                                        ) | number: '1.0-0'
                                                    }}m from pickup
                                                </p>
                                            </div>
                                            <i
                                                class="pi pi-chevron-right text-text-color-300"
                                            ></i>
                                        </button>
                                    }
                                </div>
                            </div>
                        } @else {
                            <div class="text-center py-8">
                                <i
                                    class="pi pi-info-circle text-4xl text-text-color-300 mb-4"
                                ></i>
                                <h4
                                    class="text-lg font-semibold text-text-color-100 mb-2"
                                >
                                    No nearby stop points
                                </h4>
                                <p class="text-text-color-300">
                                    No stop points found within 1km of your
                                    pickup location
                                </p>
                            </div>
                        }

                        <div class="flex gap-3">
                            <button
                                class="flex-1 flex items-center justify-center gap-2 p-4 rounded-lg border border-background-color-300 bg-background-color-100 text-text-color-100 transition-all duration-200 hover:bg-background-color-200"
                                (click)="showStopPointsSelection()"
                            >
                                <i class="pi pi-map text-sm"></i>
                                View on Map
                            </button>
                        </div>
                    </div>
                } @else {
                    <!-- Map View -->
                    <div class="flex h-auto min-h-0 flex-1 flex-col">
                        <div
                            class="p-4 border-b border-background-color-300 bg-blue-50"
                        >
                            <div class="flex items-center gap-3">
                                <i class="pi pi-info-circle text-blue-600"></i>
                                <div>
                                    <h4
                                        class="text-sm font-semibold text-blue-800"
                                    >
                                        Map Legend
                                    </h4>
                                    <div
                                        class="flex gap-4 text-xs text-blue-700 mt-1"
                                    >
                                        <span
                                            ><span
                                                class="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"
                                            ></span
                                            >Pickup</span
                                        >
                                        <span
                                            ><span
                                                class="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"
                                            ></span
                                            >Dropoff</span
                                        >
                                        <span
                                            ><span
                                                class="inline-block w-3 h-3 bg-orange-500 rounded-full mr-1"
                                            ></span
                                            >Stop Points</span
                                        >
                                    </div>
                                </div>
                                <button
                                    class="ml-auto p-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                                    (click)="hideStopPointsSelection()"
                                >
                                    <i class="pi pi-times text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <app-map
                            [nodes]="mapNodes()"
                            [options]="mapOptions()"
                            class="flex-1 h-96"
                        ></app-map>
                    </div>
                }
            } @else {
                <!-- Confirmation View -->
                <div class="flex flex-1 flex-col justify-center p-6">
                    <div class="mb-8 text-center">
                        <i
                            class="pi pi-check-circle mb-4 text-4xl text-green-600"
                        ></i>
                        <h2
                            class="mb-2 text-xl font-semibold text-text-color-100"
                        >
                            Perfect! We've got your pickup location
                        </h2>
                        <p class="text-text-color-300">
                            Your driver will meet you at this nearby location
                        </p>
                    </div>

                    <!-- Near Pickup Summary -->
                    <div class="mb-8">
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <div class="flex items-start gap-4">
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-orange-600 text-white"
                                >
                                    <i class="pi pi-map-marker text-sm"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <h4
                                        class="mb-1 text-sm font-semibold text-text-color-100"
                                    >
                                        Near Pickup Location
                                    </h4>
                                    <p class="text-sm text-text-color-300">
                                        {{
                                            nearPickupAddress() ||
                                                'Selected location'
                                        }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button
                            class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg bg-background-color-200 text-base font-semibold text-white shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400"
                            (click)="confirmNearPickup()"
                        >
                            <i class="pi pi-arrow-right mr-2 text-sm"></i>
                            Continue to Confirmation
                        </button>

                        <button
                            class="flex min-h-[48px] w-full cursor-pointer items-center justify-center rounded-lg border border-background-color-300 bg-background-color-100 text-base font-medium text-text-color-100 transition-all duration-200 hover:bg-background-color-200"
                            (click)="changeNearPickup()"
                        >
                            <i class="pi pi-pencil mr-2 text-sm"></i>
                            Change Location
                        </button>
                    </div>
                </div>
            }
        </div>
    `,
})
export class NearPickupLocationStepComponent implements OnInit {
    services = injectMany({
        NominatimService,
        StopPointService,
    });

    // Inputs
    tripData = input<ScheduledTripData>({});

    // Outputs
    completed = output<{
        location: LatLng;
        address?: string;
    }>();

    // State
    nearPickupSelected = signal<boolean>(false);
    nearPickupLocation = signal<LatLng | null>(null);
    nearPickupAddress = signal<string>('');
    allStopPoints = signal<StopPoint[]>([]);
    nearbyStopPoints = signal<StopPoint[]>([]);
    showStopPointsMap = signal<boolean>(false);

    // Computed properties
    mapNodes = computed(() => {
        const nodes = [];
        const tripData = this.tripData();

        // Add pickup location marker (green)
        if (tripData.pickupLocation) {
            const pickupMarker = marker(tripData.pickupLocation, {
                icon: divIcon({
                    iconSize: [36, 36],
                    className: '',
                    html: `<div style="background-color:#10b981" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                             <div style="background-color:#10b981" class="flex size-7 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-circle text-sm"></i>
                             </div>
                           </div>`,
                }),
            });
            nodes.push(pickupMarker);
        }

        // Add dropoff location marker (red)
        if (tripData.dropoffLocation) {
            const dropoffMarker = marker(tripData.dropoffLocation, {
                icon: divIcon({
                    iconSize: [36, 36],
                    className: '',
                    html: `<div style="background-color:#ef4444" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                             <div style="background-color:#ef4444" class="flex size-7 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-circle text-sm"></i>
                             </div>
                           </div>`,
                }),
            });
            nodes.push(dropoffMarker);
        }

        // Add nearby stop points (orange, clickable)
        this.nearbyStopPoints().forEach((stopPoint) => {
            const stopMarker = marker(
                latLng(stopPoint.latitude, stopPoint.longitude),
                {
                    icon: divIcon({
                        iconSize: [32, 32],
                        className: 'cursor-pointer',
                        html: `<div style="background-color:#f97316" class="flex size-8 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200 hover:scale-110 transition-transform">
                             <div style="background-color:#f97316" class="flex size-6 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-map-marker text-xs"></i>
                             </div>
                           </div>`,
                    }),
                },
            );

            // Add click handler for stop point selection
            stopMarker.on('click', () => {
                this.selectStopPoint(stopPoint);
            });

            nodes.push(stopMarker);
        });

        // Add selected near pickup location (blue)
        if (this.nearPickupLocation()) {
            const nearPickupMarker = marker(this.nearPickupLocation()!, {
                icon: divIcon({
                    iconSize: [36, 36],
                    className: '',
                    html: `<div style="background-color:#3b82f6" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                             <div style="background-color:#3b82f6" class="flex size-7 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-map-marker text-sm"></i>
                             </div>
                           </div>`,
                }),
            });
            nodes.push(nearPickupMarker);
        }

        // Add route line if we have pickup and dropoff
        if (tripData.pickupLocation && tripData.dropoffLocation) {
            const routePoints = [
                tripData.pickupLocation,
                tripData.dropoffLocation,
            ];

            // Add near pickup location to route if selected
            if (this.nearPickupLocation()) {
                routePoints.unshift(this.nearPickupLocation()!);
            }

            const routeLine = polyline(routePoints, {
                color: '#6366f1',
                weight: 4,
                opacity: 0.7,
                dashArray: '10, 5',
            });
            nodes.push(routeLine);
        }

        return nodes;
    });

    mapOptions = computed(() => {
        const tripData = this.tripData();
        if (tripData.pickupLocation) {
            return {
                center: tripData.pickupLocation,
                zoom: 13,
            };
        }
        return {
            center: latLng(51.505, -0.09),
            zoom: 13,
        };
    });

    constructor() {
        // Re-filter stop points when trip data changes
        effect(() => {
            const tripData = this.tripData();
            if (tripData.pickupLocation && this.allStopPoints().length > 0) {
                this.filterNearbyStopPoints();
            }
        });
    }

    ngOnInit(): void {
        this.loadStopPoints();
    }

    loadStopPoints(): void {
        this.services.StopPointService.getAllStopPoints().subscribe({
            next: (response) => {
                if (response.data) {
                    this.allStopPoints.set(response.data);
                    this.filterNearbyStopPoints();
                }
            },
            error: (error) => {
                console.error('Error loading stop points:', error);
            },
        });
    }

    filterNearbyStopPoints(): void {
        const tripData = this.tripData();
        const pickupLocation = tripData.pickupLocation;

        if (!pickupLocation) {
            this.nearbyStopPoints.set([]);
            return;
        }

        const nearby = this.allStopPoints().filter((stopPoint) => {
            const distance = this.calculateDistance(
                pickupLocation.lat,
                pickupLocation.lng,
                stopPoint.latitude,
                stopPoint.longitude,
            );
            return distance <= 1000; // 1000 meters = 1km
        });

        this.nearbyStopPoints.set(nearby);
    }

    calculateDistance(
        lat1: number,
        lng1: number,
        lat2: number,
        lng2: number,
    ): number {
        const R = 6371000; // Earth's radius in meters
        const dLat = ((lat2 - lat1) * Math.PI) / 180;
        const dLng = ((lng2 - lng1) * Math.PI) / 180;
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos((lat1 * Math.PI) / 180) *
                Math.cos((lat2 * Math.PI) / 180) *
                Math.sin(dLng / 2) *
                Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c; // Distance in meters
    }

    selectStopPoint(stopPoint: StopPoint): void {
        const location = latLng(stopPoint.latitude, stopPoint.longitude);
        this.nearPickupLocation.set(location);
        this.nearPickupAddress.set(stopPoint.name);
        this.nearPickupSelected.set(true);
        this.showStopPointsMap.set(false);
    }

    onNearPickupSelected(location: LatLng): void {
        this.nearPickupLocation.set(location);

        // Get address for the location
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.nearPickupAddress.set(result.display_name);
                }
                this.nearPickupSelected.set(true);
            },
            error: () => {
                this.nearPickupAddress.set('Selected location');
                this.nearPickupSelected.set(true);
            },
        });
    }

    confirmNearPickup(): void {
        const location = this.nearPickupLocation();
        if (location) {
            this.completed.emit({
                location,
                address: this.nearPickupAddress(),
            });
        }
    }

    changeNearPickup(): void {
        this.nearPickupSelected.set(false);
        this.nearPickupLocation.set(null);
        this.nearPickupAddress.set('');
        this.showStopPointsMap.set(false);
    }

    showStopPointsSelection(): void {
        this.showStopPointsMap.set(true);
    }

    hideStopPointsSelection(): void {
        this.showStopPointsMap.set(false);
    }
}
