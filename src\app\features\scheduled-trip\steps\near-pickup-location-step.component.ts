import { CommonModule } from '@angular/common';
import {
    Component,
    input,
    output,
    signal,
    computed,
    OnInit,
    effect,
} from '@angular/core';
import { LatLng, latLng, marker, divIcon, polyline } from 'leaflet';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import {
    StopPointService,
    StopPoint,
} from '../../../services/stop-point.service';
import { ScheduledTripData } from '../scheduled-trip-steps.component';
import { NominatimService } from '../../../services/nominatim.service';

import { ValhallaService } from '../../../services/valhala.service';
import { injectMany } from '../../../shared/helpers/injectMany';
import { MapComponent } from '../../../shared/components/map.component';
import { DirectionsRequest } from '../../../shared/types/valhalla.types';
import { parseDirectionsGeometry } from '../../../shared/helpers/helpers';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';

@Component({
    selector: 'app-near-pickup-location-step',
    standalone: true,
    imports: [CommonModule, ButtonModule, CardModule, MapComponent],
    template: `
        <div class="flex flex-1 flex-col bg-background-color-100">
            <!-- Step Header -->
            <div
                class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
            >
                <div
                    class="flex h-12 w-12 items-center justify-center rounded-full bg-main-color-600 text-white"
                >
                    <i class="pi pi-map-marker text-lg"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-text-color-100">
                        Where should we pick you up nearby?
                    </h3>
                    <p class="text-sm text-text-color-300">
                        Choose a convenient location near your pickup point
                    </p>
                </div>
            </div>

            <!-- Trip Summary -->
            <div
                class="border-b border-background-color-300 bg-background-color-100 p-4"
            >
                <div class="space-y-3">
                    <!-- Original Pickup -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-white"
                        >
                            <i class="pi pi-circle text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Original Pickup
                            </h4>
                            <p class="truncate text-sm text-text-color-300">
                                {{
                                    tripData().pickupAddress ||
                                        'Selected location'
                                }}
                            </p>
                        </div>
                    </div>

                    <!-- Destination -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-red-600 text-white"
                        >
                            <i class="pi pi-circle text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Destination
                            </h4>
                            <p class="truncate text-sm text-text-color-300">
                                {{
                                    tripData().dropoffAddress ||
                                        'Selected location'
                                }}
                            </p>
                        </div>
                    </div>

                    <!-- Scheduled Time -->
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white"
                        >
                            <i class="pi pi-clock text-xs"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-sm font-medium text-text-color-100">
                                Scheduled Time
                            </h4>
                            <p class="text-sm text-text-color-300">
                                {{
                                    tripData().scheduledDateTime
                                        | date: 'medium'
                                }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="border-b border-background-color-300 bg-blue-50 p-4">
                <div class="flex items-start gap-3">
                    <i class="pi pi-info-circle mt-0.5 text-blue-600"></i>
                    <div>
                        <h4 class="mb-1 text-sm font-semibold text-blue-800">
                            Why choose a nearby pickup location?
                        </h4>
                        <p class="text-sm text-blue-700">
                            Selecting a nearby location helps ensure your driver
                            can easily find you and provides flexibility for
                            your scheduled pickup time.
                        </p>
                    </div>
                </div>
            </div>

            @if (!nearPickupSelected()) {
                <!-- Near Pickup Location Selection -->
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-600 text-white"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Select nearby pickup location
                        </h3>
                    </div>
                </div>

                <div class="flex h-auto min-h-0 flex-1 flex-col">
                    <div
                        class="p-4 border-b border-background-color-300 bg-blue-50"
                    >
                        <div class="flex items-center gap-3">
                            <i class="pi pi-info-circle text-blue-600"></i>
                            <div>
                                <h4 class="text-sm font-semibold text-blue-800">
                                    Map Legend
                                </h4>
                                <div
                                    class="flex gap-4 text-xs text-blue-700 mt-1"
                                >
                                    <span
                                        ><span
                                            class="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"
                                        ></span
                                        >Pickup</span
                                    >
                                    <span
                                        ><span
                                            class="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"
                                        ></span
                                        >Dropoff</span
                                    >
                                    <span
                                        ><span
                                            class="inline-block w-3 h-3 bg-orange-500 rounded-full mr-1"
                                        ></span
                                        >Stop Points (Click to Select)</span
                                    >
                                    @if (nearPickupLocation()) {
                                        <span
                                            ><span
                                                class="inline-block w-3 h-3 bg-blue-500 rounded-full mr-1"
                                            ></span
                                            >Selected Near Pickup</span
                                        >
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 h-96">
                        <app-map
                            [nodes]="mapNodes()"
                            [options]="mapOptions()"
                            class="w-full h-full"
                        ></app-map>
                    </div>
                </div>
            } @else {
                <!-- Confirmation View -->
                <div class="flex flex-1 flex-col justify-center p-6">
                    <div class="mb-8 text-center">
                        <i
                            class="pi pi-check-circle mb-4 text-4xl text-green-600"
                        ></i>
                        <h2
                            class="mb-2 text-xl font-semibold text-text-color-100"
                        >
                            Perfect! We've got your pickup location
                        </h2>
                        <p class="text-text-color-300">
                            Your driver will meet you at this nearby location
                        </p>
                    </div>

                    <!-- Near Pickup Summary -->
                    <div class="mb-8">
                        <div
                            class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                        >
                            <div class="flex items-start gap-4">
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-orange-600 text-white"
                                >
                                    <i class="pi pi-map-marker text-sm"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <h4
                                        class="mb-1 text-sm font-semibold text-text-color-100"
                                    >
                                        Near Pickup Location
                                    </h4>
                                    <p class="text-sm text-text-color-300">
                                        {{
                                            nearPickupAddress() ||
                                                'Selected location'
                                        }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button
                            class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg bg-background-color-200 text-base font-semibold text-white shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400"
                            (click)="confirmNearPickup()"
                        >
                            <i class="pi pi-arrow-right mr-2 text-sm"></i>
                            Continue to Confirmation
                        </button>

                        <button
                            class="flex min-h-[48px] w-full cursor-pointer items-center justify-center rounded-lg border border-background-color-300 bg-background-color-100 text-base font-medium text-text-color-100 transition-all duration-200 hover:bg-background-color-200"
                            (click)="changeNearPickup()"
                        >
                            <i class="pi pi-pencil mr-2 text-sm"></i>
                            Change Location
                        </button>
                    </div>
                </div>
            }
        </div>
    `,
})
export class NearPickupLocationStepComponent implements OnInit {
    services = injectMany({
        NominatimService,
        ValhallaService,
        StopPointService,
    });

    // Inputs
    tripData = input<ScheduledTripData>({});

    // Outputs
    completed = output<{
        location: LatLng;
        address?: string;
    }>();

    // State
    nearPickupSelected = signal<boolean>(false);
    nearPickupLocation = signal<LatLng | null>(null);
    nearPickupAddress = signal<string>('');
    routePolylines = signal<any[]>([]);
    allStopPoints = signal<StopPoint[]>([]);

    // Computed properties
    mapNodes = computed(() => {
        const nodes = [];
        const tripData = this.tripData();

        // Add pickup location marker (green)
        if (tripData.pickupLocation) {
            const pickupMarker = marker(tripData.pickupLocation, {
                icon: divIcon({
                    iconSize: [36, 36],
                    className: '',
                    html: `<div style="background-color:#10b981" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                             <div style="background-color:#10b981" class="flex size-7 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-circle text-sm"></i>
                             </div>
                           </div>`,
                }),
            });
            nodes.push(pickupMarker);
        }

        // Add dropoff location marker (red)
        if (tripData.dropoffLocation) {
            const dropoffMarker = marker(tripData.dropoffLocation, {
                icon: divIcon({
                    iconSize: [36, 36],
                    className: '',
                    html: `<div style="background-color:#ef4444" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                             <div style="background-color:#ef4444" class="flex size-7 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-circle text-sm"></i>
                             </div>
                           </div>`,
                }),
            });
            dropoffMarker.bindPopup(`
                <div class="p-2">
                    <div class="text-sm font-semibold text-red-800">Dropoff Location</div>
                    <div class="text-xs text-gray-600">${tripData.dropoffAddress || 'Selected dropoff point'}</div>
                </div>
            `);
            nodes.push(dropoffMarker);
        }

        // Add all stop points as clickable markers (orange)
        this.allStopPoints().forEach((stopPoint) => {
            const stopMarker = marker(
                latLng(stopPoint.latitude, stopPoint.longitude),
                {
                    icon: divIcon({
                        iconSize: [32, 32],
                        className: 'cursor-pointer',
                        html: `<div style="background-color:#f97316" class="flex size-8 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200 hover:scale-110 transition-transform">
                             <div style="background-color:#f97316" class="flex size-6 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-map-marker text-xs"></i>
                             </div>
                           </div>`,
                    }),
                },
            );

            // Add popup with stop point info
            stopMarker.bindPopup(`
                <div class="p-2">
                    <div class="text-sm font-semibold text-orange-800">${stopPoint.name}</div>
                    <div class="text-xs text-gray-600 mb-2">Click to select as near pickup location</div>
                </div>
            `);

            // Add click handler for stop point selection
            stopMarker.on('click', () => {
                this.selectStopPoint(stopPoint);
            });

            nodes.push(stopMarker);
        });

        // Add selected near pickup location (blue)
        if (this.nearPickupLocation()) {
            const nearPickupMarker = marker(this.nearPickupLocation()!, {
                icon: divIcon({
                    iconSize: [36, 36],
                    className: '',
                    html: `<div style="background-color:#3b82f6" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                             <div style="background-color:#3b82f6" class="flex size-7 items-center justify-center rounded-full text-white shadow-shadow-300">
                               <i class="pi pi-map-marker text-sm"></i>
                             </div>
                           </div>`,
                }),
            });
            nodes.push(nearPickupMarker);
        }

        // Add route polylines from Valhalla
        this.routePolylines().forEach((polylineLayer) => {
            nodes.push(polylineLayer);
        });

        return nodes;
    });

    mapOptions = computed(() => {
        const tripData = this.tripData();
        if (tripData.pickupLocation) {
            return {
                center: tripData.pickupLocation,
                zoom: 13,
            };
        }
        return {
            center: latLng(51.505, -0.09),
            zoom: 13,
        };
    });

    constructor() {
        // Update route when locations change
        effect(() => {
            const tripData = this.tripData();
            const nearPickup = this.nearPickupLocation();

            if (tripData.pickupLocation && tripData.dropoffLocation) {
                this.updateRoute(tripData, nearPickup);
            }
        });
    }

    ngOnInit(): void {
        this.loadStopPoints();
    }

    loadStopPoints(): void {
        this.services.StopPointService.getAllStopPoints().subscribe({
            next: (response) => {
                if (response.data) {
                    this.allStopPoints.set(response.data);
                }
            },
            error: (error) => {
                console.error('Error loading stop points:', error);
            },
        });
    }

    selectStopPoint(stopPoint: StopPoint): void {
        const location = latLng(stopPoint.latitude, stopPoint.longitude);
        this.nearPickupLocation.set(location);
        this.nearPickupAddress.set(stopPoint.name);
        this.nearPickupSelected.set(true);
    }

    onNearPickupSelected(location: LatLng): void {
        this.nearPickupLocation.set(location);

        // Get address for the location
        this.services.NominatimService.reverseGeocode(
            location.lng,
            location.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.nearPickupAddress.set(result.display_name);
                }
                this.nearPickupSelected.set(true);
            },
            error: () => {
                this.nearPickupAddress.set('Selected location');
                this.nearPickupSelected.set(true);
            },
        });
    }

    confirmNearPickup(): void {
        const location = this.nearPickupLocation();
        if (location) {
            this.completed.emit({
                location,
                address: this.nearPickupAddress(),
            });
        }
    }

    changeNearPickup(): void {
        this.nearPickupSelected.set(false);
        this.nearPickupLocation.set(null);
        this.nearPickupAddress.set('');

        // Route will be updated automatically by the effect
    }

    updateRoute(tripData: ScheduledTripData, nearPickup: LatLng | null): void {
        const locations = [];

        // Add near pickup location if selected, otherwise use original pickup
        if (nearPickup) {
            locations.push({ lat: nearPickup.lat, lon: nearPickup.lng });
        }

        // Always add original pickup location
        locations.push({
            lat: tripData.pickupLocation!.lat,
            lon: tripData.pickupLocation!.lng,
        });

        // Add dropoff location
        locations.push({
            lat: tripData.dropoffLocation!.lat,
            lon: tripData.dropoffLocation!.lng,
        });

        // Only create route if we have at least 2 locations
        if (locations.length >= 2) {
            this.getValhallaRoute(locations);
        }
    }

    getValhallaRoute(locations: { lat: number; lon: number }[]): void {
        const directionsRequest: DirectionsRequest = {
            locations,
            costing: 'auto',
            directions_options: {
                units: 'kilometers',
                language: 'en',
            },
        };

        this.services.ValhallaService.getDirections(directionsRequest)
            .pipe(
                catchError((error) => {
                    console.error('Error getting route from Valhalla:', error);
                    // Fallback to simple straight lines
                    this.createFallbackRoute(locations);
                    return of(null);
                }),
            )
            .subscribe((response) => {
                if (response && response.trip && response.trip.legs) {
                    this.createRouteFromValhalla(response.trip.legs);
                }
            });
    }

    createRouteFromValhalla(legs: any[]): void {
        const polylines: any[] = [];

        legs.forEach((leg, index) => {
            if (leg.shape) {
                try {
                    const coordinates = parseDirectionsGeometry([leg]);
                    if (coordinates.length > 0) {
                        const routeLine = polyline(coordinates, {
                            color: index === 0 ? '#f97316' : '#6366f1', // Orange for first leg, blue for others
                            weight: 4,
                            opacity: 0.8,
                            smoothFactor: 1,
                        });

                        // Add popup with route information
                        if (leg.summary) {
                            routeLine.bindPopup(`
                                <div class="p-2">
                                    <div class="text-sm font-semibold mb-1">Route Segment ${index + 1}</div>
                                    <div class="text-xs">Distance: ${leg.summary.length?.toFixed(1) || 'N/A'} km</div>
                                    <div class="text-xs">Time: ${Math.round((leg.summary.time || 0) / 60)} min</div>
                                </div>
                            `);
                        }

                        polylines.push(routeLine);
                    }
                } catch (error) {
                    console.warn(
                        'Failed to decode polyline for leg:',
                        index,
                        error,
                    );
                }
            }
        });

        this.routePolylines.set(polylines);
    }

    createFallbackRoute(locations: { lat: number; lon: number }[]): void {
        const polylines: any[] = [];

        for (let i = 0; i < locations.length - 1; i++) {
            const start = locations[i];
            const end = locations[i + 1];

            const routeLine = polyline(
                [
                    [start.lat, start.lon],
                    [end.lat, end.lon],
                ],
                {
                    color: i === 0 ? '#f97316' : '#6366f1',
                    weight: 4,
                    opacity: 0.7,
                    dashArray: '10, 5', // Dashed to indicate fallback
                },
            );

            routeLine.bindPopup(`
                <div class="p-2">
                    <div class="text-sm font-semibold mb-1">Direct Route ${i + 1}</div>
                    <div class="text-xs text-orange-600">Fallback route (Valhalla unavailable)</div>
                </div>
            `);

            polylines.push(routeLine);
        }

        this.routePolylines.set(polylines);
    }
}
