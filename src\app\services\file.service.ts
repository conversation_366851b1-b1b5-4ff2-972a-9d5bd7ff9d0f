import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class FileService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Get an image file by filename
     * @param filename Name of the image file
     * @returns Observable with image blob data
     */
    getImage(
        filename: string,
    ): Observable<{ data: Blob; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/files/images/${filename}`,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for file downloads
            extra: {
                responseType: 'blob', // Important for binary data
                headers: {
                    'Accept': 'image/jpeg, image/png, image/gif, image/*'
                }
            },
        };
        return this.http.get<Blob>(options);
    }

    /**
     * Get image URL for display
     * @param filename Name of the image file
     * @returns Full URL to the image
     */
    getImageUrl(filename: string): string {
        return `${this.http.server}api/files/images/${filename}`;
    }

    /**
     * Get static file URL from uploads directory
     * @param filename Name of the file
     * @returns Full URL to the static file
     */
    getStaticFileUrl(filename: string): string {
        return `${this.http.server}uploads/${filename}`;
    }

    /**
     * Download a file
     * @param filename Name of the file to download
     * @param endpoint Optional custom endpoint (defaults to files/images)
     * @returns Observable with file blob data
     */
    downloadFile(
        filename: string,
        endpoint: string = 'api/files/images',
    ): Observable<{ data: Blob; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `${endpoint}/${filename}`,
            des: this.destroyRef,
            successToast: false,
            extra: {
                responseType: 'blob',
            },
        };
        return this.http.get<Blob>(options);
    }

    /**
     * Helper method to create object URL from blob for display
     * @param blob Blob data
     * @returns Object URL string
     */
    createObjectUrl(blob: Blob): string {
        return URL.createObjectURL(blob);
    }

    /**
     * Helper method to revoke object URL to free memory
     * @param url Object URL to revoke
     */
    revokeObjectUrl(url: string): void {
        URL.revokeObjectURL(url);
    }
}
