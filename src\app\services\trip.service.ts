import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    Trip,
    TripStatus,
    DriverLocationDto,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export { TripStatus } from '../core/types/tripoos.types';
export type {
    Trip,
    DriverLocationDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class TripService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Mark driver as arrived at pickup location
     * @param tripId ID of the trip
     * @returns Observable with updated Trip data
     */
    markDriverArrived(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/trips/${tripId}/arrived`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    /**
     * Start a trip
     * @param tripId ID of the trip to start
     * @returns Observable with updated Trip data
     */
    startTrip(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/trips/${tripId}/start`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    /**
     * Update trip location
     * @param tripId ID of the trip
     * @param locationDto Driver's current location
     * @returns Observable with updated Trip data
     */
    updateTripLocation(
        tripId: string,
        locationDto: DriverLocationDto,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/trips/${tripId}/location`,
            opj: locationDto,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }

    /**
     * Get trip by ID
     * @param tripId ID of the trip
     * @returns Observable with Trip data
     */
    getTripById(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/trips/${tripId}`,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<Trip>(options);
    }

    /**
     * Complete a trip
     * @param tripId ID of the trip to complete
     * @returns Observable with updated Trip data
     */
    completeTrip(
        tripId: string,
    ): Observable<{ data: Trip; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/trips/${tripId}/complete`,
            des: this.destroyRef,
        };
        return this.http.post<Trip>(options);
    }
}
