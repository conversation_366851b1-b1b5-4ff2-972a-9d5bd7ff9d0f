import { Controller, Post, Body, UseGuards, Get, Param } from '@nestjs/common';
import { TripService } from './trip.service';
import { DriverGuard } from '../common/guards/driver.guard';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User, Trip } from '@prisma/client';
import { DriverLocationDto } from './dto';

@Controller('api/trips')
export class TripController {
  constructor(private readonly tripService: TripService) {}

  @Post(':tripId/arrived')
  @UseGuards(DriverGuard)
  markDriverArrived(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.tripService.markDriverArrived(tripId, driver.id);
  }

  @Post(':tripId/start')
  @UseGuards(DriverGuard)
  startTrip(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.tripService.startTrip(tripId, driver.id);
  }

  @Post(':tripId/location')
  @UseGuards(DriverGuard)
  updateTripLocation(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
    @Body() locationDto: DriverLocationDto,
  ): Promise<Trip> {
    return this.tripService.updateTripLocation(
      tripId,
      driver.id,
      locationDto.latitude,
      locationDto.longitude,
    );
  }

  @Get(':tripId')
  getTripById(@Param('tripId') tripId: string): Promise<Trip> {
    return this.tripService.getTripById(tripId);
  }

  @Post(':tripId/complete')
  @UseGuards(DriverGuard)
  completeTrip(
    @Param('tripId') tripId: string,
    @GetUser() driver: User,
  ): Promise<Trip> {
    return this.tripService.completeTrip(tripId, driver.id);
  }
}
